/**
 * Grid Manager
 * Handles the creation and management of the 4x6 activation grid
 */

class GridManager {
    constructor(config) {
        this.config = config;
        this.gridContainer = document.getElementById('activationGrid');
        this.cells = new Map();
        this.currentMode = 'picture';
    }

    async createGrid() {
        this.gridContainer.innerHTML = '';

        for (let i = 1; i <= this.config.grid.totalCells; i++) {
            const cell = await this.createGridCell(i);
            this.cells.set(i, cell);
            this.gridContainer.appendChild(cell.element);
        }
    }

    async createGridCell(index) {
        const cellElement = document.createElement('div');
        cellElement.className = 'grid-cell';
        cellElement.setAttribute('data-cell-index', index);

        // Create cell label
        const label = document.createElement('div');
        label.className = 'cell-label';
        label.textContent = `Cell ${index}`;
        cellElement.appendChild(label);

        // Create image element
        const img = document.createElement('img');
        img.className = 'cell-image';
        img.alt = `Base image ${index}`;

        // Try to load base image
        const baseImagePath = await this.findImageFile(this.config.paths.baseImages, `baseimage${index}`);
        if (baseImagePath) {
            img.src = baseImagePath;
        } else {
            // Create placeholder if no image found
            img.src = this.createPlaceholderImage(index);
        }

        cellElement.appendChild(img);

        // Create video element
        const video = document.createElement('video');
        video.className = 'cell-video';
        video.muted = this.config.video.muted;
        video.loop = this.config.video.loop;
        video.autoplay = this.config.video.autoplay;
        video.controls = this.config.video.controls;

        // Try to load video
        const videoPath = await this.findVideoFile(this.config.paths.videos, 'main');
        if (videoPath) {
            video.src = videoPath;
        }

        cellElement.appendChild(video);

        // Create activation overlay canvas
        const overlayCanvas = document.createElement('canvas');
        overlayCanvas.className = 'activation-overlay';
        cellElement.appendChild(overlayCanvas);

        // Add click event listener for opening in new tab
        cellElement.addEventListener('click', () => {
            this.openCellInNewTab(index);
        });

        return {
            element: cellElement,
            image: img,
            video: video,
            overlay: overlayCanvas,
            index: index
        };
    }

    async findImageFile(basePath, filename) {
        for (const ext of this.config.fileFormats.images) {
            const path = `${basePath}${filename}.${ext}`;
            if (await this.fileExists(path)) {
                return path;
            }
        }
        return null;
    }

    async findVideoFile(basePath, filename) {
        for (const ext of this.config.fileFormats.videos) {
            const path = `${basePath}${filename}.${ext}`;
            if (await this.fileExists(path)) {
                return path;
            }
        }
        return null;
    }

    async fileExists(path) {
        try {
            const response = await fetch(path, { method: 'HEAD' });
            return response.ok;
        } catch {
            return false;
        }
    }

    createPlaceholderImage(index) {
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 400;
        const ctx = canvas.getContext('2d');

        // Create gradient background
        const gradient = ctx.createLinearGradient(0, 0, 400, 400);
        gradient.addColorStop(0, '#333333');
        gradient.addColorStop(1, '#555555');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 400, 400);

        // Add text
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(`Base Image ${index}`, 200, 180);
        ctx.fillText('Not Found', 200, 220);

        return canvas.toDataURL();
    }

    switchMode(mode) {
        this.currentMode = mode;

        this.cells.forEach((cell) => {
            if (mode === 'video') {
                cell.element.classList.add('video-mode');
                if (cell.video.src) {
                    cell.video.play().catch(e => console.warn('Video autoplay failed:', e));
                }
            } else {
                cell.element.classList.remove('video-mode');
                if (cell.video.src) {
                    cell.video.pause();
                }
            }
        });

        // Also update fullscreen grid if active
        this.updateFullscreenMode(mode);
    }

    updateFullscreenMode(mode) {
        const fullscreenGrid = document.getElementById('fullscreenGrid');
        if (fullscreenGrid) {
            const fullscreenCells = fullscreenGrid.querySelectorAll('.grid-cell');
            fullscreenCells.forEach((cell) => {
                const video = cell.querySelector('video');
                if (mode === 'video') {
                    cell.classList.add('video-mode');
                    if (video && video.src) {
                        video.play().catch(e => console.warn('Fullscreen video autoplay failed:', e));
                    }
                } else {
                    cell.classList.remove('video-mode');
                    if (video && video.src) {
                        video.pause();
                    }
                }
            });
        }
    }

    setActivationOverlay(cellIndex, overlayCanvas) {
        const cell = this.cells.get(cellIndex);
        if (!cell) return;

        const targetCanvas = cell.overlay;
        const targetCtx = targetCanvas.getContext('2d');

        // Set canvas size to match cell dimensions exactly
        const cellRect = cell.element.getBoundingClientRect();
        const cellWidth = cellRect.width || 200;
        const cellHeight = cellRect.height || 200;

        targetCanvas.width = cellWidth;
        targetCanvas.height = cellHeight;

        // Clear and draw the activation overlay to fit the entire cell
        targetCtx.clearRect(0, 0, cellWidth, cellHeight);

        // Use object-fit: cover behavior - scale to fill while maintaining aspect ratio
        const overlayAspect = overlayCanvas.width / overlayCanvas.height;
        const cellAspect = cellWidth / cellHeight;

        let drawWidth, drawHeight, drawX, drawY;

        if (overlayAspect > cellAspect) {
            // Overlay is wider - fit to height and crop width
            drawHeight = cellHeight;
            drawWidth = cellHeight * overlayAspect;
            drawX = (cellWidth - drawWidth) / 2;
            drawY = 0;
        } else {
            // Overlay is taller - fit to width and crop height
            drawWidth = cellWidth;
            drawHeight = cellWidth / overlayAspect;
            drawX = 0;
            drawY = (cellHeight - drawHeight) / 2;
        }

        targetCtx.drawImage(overlayCanvas, drawX, drawY, drawWidth, drawHeight);

        // Also update fullscreen overlay if fullscreen is active
        this.updateFullscreenOverlay(cellIndex, overlayCanvas);
    }

    updateFullscreenOverlay(cellIndex, overlayCanvas) {
        const fullscreenGrid = document.getElementById('fullscreenGrid');
        if (!fullscreenGrid || !fullscreenGrid.children.length) return;

        const fullscreenCells = fullscreenGrid.querySelectorAll('.grid-cell');
        const targetCell = fullscreenCells[cellIndex - 1]; // cellIndex is 1-based

        if (targetCell) {
            const targetCanvas = targetCell.querySelector('.activation-overlay');
            if (targetCanvas) {
                const targetCtx = targetCanvas.getContext('2d');

                // Set canvas size to match cell dimensions exactly
                const cellRect = targetCell.getBoundingClientRect();
                const cellWidth = cellRect.width || 200;
                const cellHeight = cellRect.height || 200;

                targetCanvas.width = cellWidth;
                targetCanvas.height = cellHeight;

                // Clear and draw the activation overlay to fit the entire cell
                targetCtx.clearRect(0, 0, cellWidth, cellHeight);

                // Use object-fit: cover behavior - scale to fill while maintaining aspect ratio
                const overlayAspect = overlayCanvas.width / overlayCanvas.height;
                const cellAspect = cellWidth / cellHeight;

                let drawWidth, drawHeight, drawX, drawY;

                if (overlayAspect > cellAspect) {
                    // Overlay is wider - fit to height and crop width
                    drawHeight = cellHeight;
                    drawWidth = cellHeight * overlayAspect;
                    drawX = (cellWidth - drawWidth) / 2;
                    drawY = 0;
                } else {
                    // Overlay is taller - fit to width and crop height
                    drawWidth = cellWidth;
                    drawHeight = cellWidth / overlayAspect;
                    drawX = 0;
                    drawY = (cellHeight - drawHeight) / 2;
                }

                targetCtx.drawImage(overlayCanvas, drawX, drawY, drawWidth, drawHeight);
            }
        }
    }

    getCell(index) {
        return this.cells.get(index);
    }

    getAllCells() {
        return Array.from(this.cells.values());
    }

    updateCellImage(index, imageSrc) {
        const cell = this.cells.get(index);
        if (cell) {
            cell.image.src = imageSrc;
        }
    }

    updateCellVideo(index, videoSrc) {
        const cell = this.cells.get(index);
        if (cell) {
            cell.video.src = videoSrc;
            if (this.currentMode === 'video') {
                cell.video.play().catch(e => console.warn('Video play failed:', e));
            }
        }
    }

    clearActivationOverlay(cellIndex) {
        const cell = this.cells.get(cellIndex);
        if (cell) {
            const ctx = cell.overlay.getContext('2d');
            ctx.clearRect(0, 0, cell.overlay.width, cell.overlay.height);
        }
    }

    clearAllActivationOverlays() {
        this.cells.forEach((_, index) => {
            this.clearActivationOverlay(index);
        });
    }

    openCellInNewTab(cellIndex) {
        const cell = this.cells.get(cellIndex);
        if (!cell) return;

        // Create a canvas to capture the current cell content
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Get cell dimensions
        const cellRect = cell.element.getBoundingClientRect();
        canvas.width = cellRect.width || 400;
        canvas.height = cellRect.height || 400;

        // Determine which content to capture based on current mode
        let sourceElement;
        if (this.currentMode === 'video' && cell.video.src) {
            sourceElement = cell.video;
        } else {
            sourceElement = cell.image;
        }

        // Draw the base content
        if (sourceElement.complete || sourceElement.readyState >= 2) {
            // Calculate object-fit: cover positioning
            const sourceAspect = sourceElement.naturalWidth / sourceElement.naturalHeight || sourceElement.videoWidth / sourceElement.videoHeight || 1;
            const canvasAspect = canvas.width / canvas.height;

            let drawWidth, drawHeight, drawX, drawY;

            if (sourceAspect > canvasAspect) {
                // Source is wider - fit to height and crop width
                drawHeight = canvas.height;
                drawWidth = canvas.height * sourceAspect;
                drawX = (canvas.width - drawWidth) / 2;
                drawY = 0;
            } else {
                // Source is taller - fit to width and crop height
                drawWidth = canvas.width;
                drawHeight = canvas.width / sourceAspect;
                drawX = 0;
                drawY = (canvas.height - drawHeight) / 2;
            }

            ctx.drawImage(sourceElement, drawX, drawY, drawWidth, drawHeight);
        }

        // Draw the activation overlay on top
        if (cell.overlay.width > 0 && cell.overlay.height > 0) {
            ctx.globalCompositeOperation = 'multiply';
            ctx.globalAlpha = 0.7;
            ctx.drawImage(cell.overlay, 0, 0, canvas.width, canvas.height);
        }

        // Convert canvas to data URL and open in new tab
        const dataURL = canvas.toDataURL('image/png');
        const newWindow = window.open();
        if (newWindow) {
            newWindow.document.write(`
                <html>
                    <head>
                        <title>Cell ${cellIndex} - Activation Map</title>
                        <style>
                            body {
                                margin: 0;
                                padding: 0;
                                background: #000;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                min-height: 100vh;
                                font-family: 'Gill Sans', sans-serif;
                            }
                            img {
                                width: 90vw;
                                height: 90vh;
                                object-fit: contain;
                            }
                            h1 {
                                position: absolute;
                                top: 20px;
                                left: 20px;
                                color: #66b3ff;
                                margin: 0;
                                font-size: 1.2em;
                                z-index: 10;
                            }
                        </style>
                    </head>
                    <body>
                        <h1>Cell ${cellIndex} - Activation Map</h1>
                        <img src="${dataURL}" alt="Cell ${cellIndex} with activation overlay" />
                    </body>
                </html>
            `);
        }
    }
}